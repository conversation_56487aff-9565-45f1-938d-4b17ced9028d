# MEDDIC-Lite Qualification Framework

## Overview
MEDDIC-Lite is our simplified qualification framework for RP9 sales opportunities. It helps identify and qualify prospects quickly while ensuring we focus on deals with the highest probability of closing.

## The MEDDIC-Lite Framework

### M - Metrics (Business Impact)
**What measurable business impact can RP9 deliver?**

#### Key Questions to Ask:
- How many hours per month does your team spend on manual, repetitive tasks?
- What's the average hourly cost of your team members?
- How many errors occur due to manual processes?
- What's the cost of these errors (time, money, customer satisfaction)?
- How would you measure the success of automation?

#### Red Flags:
- Can't quantify current pain or desired outcomes
- No clear metrics for success
- Unrealistic expectations about savings

#### Scoring (1-5):
- **5**: Clear, quantifiable metrics with significant impact potential ($50k+ annual savings)
- **4**: Good understanding of metrics with moderate impact ($20k-50k annual savings)
- **3**: Some metrics identified but limited impact ($10k-20k annual savings)
- **2**: Vague understanding of potential impact
- **1**: No clear metrics or minimal impact potential

---

### E - Economic Buyer
**Who has the authority and budget to purchase RP9?**

#### Key Questions to Ask:
- Who typically approves software purchases in your price range?
- What's the approval process for new tools?
- Who controls the budget for operational efficiency tools?
- Have you purchased similar tools before? Who was involved?

#### Buyer Personas by Vertical:
- **Contact Centers**: Head of Operations, VP Customer Service
- **Finance/Accounting**: CFO, Finance Director, Accounting Manager
- **General**: CEO/Founder (small companies), COO, Head of Operations

#### Red Flags:
- Only speaking to end users without access to decision makers
- Complex approval process with many stakeholders
- No clear budget owner

#### Scoring (1-5):
- **5**: Direct access to economic buyer who has confirmed budget
- **4**: Access to economic buyer, budget likely available
- **3**: Know who the economic buyer is, working on access
- **2**: Multiple potential buyers, unclear authority
- **1**: No clear economic buyer identified

---

### D - Decision Criteria
**What criteria will they use to evaluate and select a solution?**

#### Key Questions to Ask:
- What factors are most important in choosing an automation solution?
- How will you evaluate different options?
- What would make you choose one solution over another?
- Are you looking at other alternatives?

#### Common Decision Criteria:
- **Ease of implementation** (setup time, learning curve)
- **ROI/payback period** (how quickly they see results)
- **Integration capabilities** (works with existing tools)
- **Support quality** (responsiveness, expertise)
- **Scalability** (grows with their business)
- **Security** (data protection, compliance)
- **Price/value** (cost vs benefits)

#### Red Flags:
- Price is the only consideration
- Unrealistic technical requirements
- Decision criteria keep changing

#### Scoring (1-5):
- **5**: Clear criteria that heavily favor RP9's strengths
- **4**: Most criteria favor RP9, some neutral
- **3**: Mixed criteria, RP9 competitive
- **2**: Some criteria favor competitors
- **1**: Criteria strongly favor alternatives

---

### D - Decision Process
**What's their process for evaluating and purchasing?**

#### Key Questions to Ask:
- How do you typically evaluate new software?
- Who else needs to be involved in this decision?
- What's your timeline for making a decision?
- What steps need to happen before you can move forward?

#### Typical Process:
1. **Initial Research** (recognize problem, research solutions)
2. **Vendor Evaluation** (demos, trials, references)
3. **Internal Approval** (present to stakeholders, get buy-in)
4. **Technical Validation** (pilot/proof of concept)
5. **Contract Negotiation** (terms, pricing, implementation)

#### Red Flags:
- No defined process or timeline
- Too many stakeholders without clear roles
- Unrealistic timeline expectations

#### Scoring (1-5):
- **5**: Clear process with realistic timeline (< 60 days)
- **4**: Good process understanding, reasonable timeline
- **3**: Some process defined, timeline uncertain
- **2**: Vague process, unclear timeline
- **1**: No clear process or decision paralysis

---

### I - Identify Pain
**What specific pain points does RP9 solve?**

#### Key Questions to Ask:
- What manual processes are causing the most frustration?
- What happens when these processes fail or slow down?
- How much time does your team waste on repetitive tasks?
- What would happen if you don't solve this problem?

#### Common Pain Points by Vertical:
- **Contact Centers**: Long response times, inconsistent service, agent burnout
- **Finance**: Manual data entry errors, delayed reporting, compliance risks
- **General**: Team inefficiency, scaling challenges, human errors

#### Pain Intensity Levels:
- **Critical**: Business impact, revenue loss, compliance risk
- **Important**: Efficiency impact, team frustration, growth limitation
- **Nice to have**: Minor improvements, convenience

#### Red Flags:
- No clear pain or urgency
- Pain is too small for our price point
- Solution-shopping without real problem

#### Scoring (1-5):
- **5**: Critical pain with clear business impact and urgency
- **4**: Important pain affecting operations significantly
- **3**: Moderate pain with some business impact
- **2**: Minor pain, nice-to-have solution
- **1**: No clear pain or very low priority

---

### C - Champion
**Who internally supports our solution and will help us sell?**

#### Key Questions to Ask:
- Who would benefit most from this solution?
- Who has tried to solve this problem before?
- Who would be willing to speak with other stakeholders about the benefits?
- Who has influence with the decision maker?

#### Champion Characteristics:
- **Personally benefits** from the solution
- **Has credibility** with the economic buyer
- **Is motivated** to make change happen
- **Can provide access** to other stakeholders
- **Will advocate** for our solution

#### Champion Types:
- **The Frustrated User**: Directly affected by current pain
- **The Efficiency Expert**: Focused on operational improvement
- **The Innovation Leader**: Drives technology adoption

#### Red Flags:
- No internal support or advocates
- Only low-level champions without influence
- Champion is not committed to change

#### Scoring (1-5):
- **5**: Strong champion with influence over economic buyer
- **4**: Good champion with some influence and commitment
- **3**: Willing champion but limited influence
- **2**: Weak champion or questionable commitment
- **1**: No champion or internal resistance

---

## MEDDIC-Lite Scoring

### Overall Qualification Score
Add up individual scores (max 30 points):

- **25-30 points**: **Highly Qualified** - Prioritize and fast-track
- **20-24 points**: **Well Qualified** - Good opportunity, proceed normally
- **15-19 points**: **Moderately Qualified** - Needs development or nurturing
- **10-14 points**: **Poorly Qualified** - Consider disqualifying or long-term nurture
- **Below 10**: **Disqualify** - Not a good fit for RP9

### Action Based on Score

#### Highly Qualified (25-30)
- **Immediate Actions**: Schedule demo within 1 week, prepare custom ROI analysis
- **Timeline**: Aim to close within 30-45 days
- **Resources**: Full sales support, executive involvement if needed
- **Follow-up**: Daily/every other day

#### Well Qualified (20-24)
- **Immediate Actions**: Schedule demo within 2 weeks, standard qualification
- **Timeline**: 45-60 day sales cycle
- **Resources**: Standard sales process, marketing support
- **Follow-up**: 2-3 times per week

#### Moderately Qualified (15-19)
- **Immediate Actions**: Nurture and develop, focus on missing qualification areas
- **Timeline**: 60-90+ day cycle or nurture
- **Resources**: Marketing content, educational webinars
- **Follow-up**: Weekly follow-up

#### Poorly Qualified (10-14)
- **Immediate Actions**: Determine if qualification can be improved
- **Timeline**: Long-term nurture or disqualify
- **Resources**: Automated marketing nurture
- **Follow-up**: Monthly check-ins

---

## Qualification Call Script

### Opening (2-3 minutes)
"Thank you for taking the time to speak with me today. I've reviewed your inquiry about RP9, and I'd love to learn more about your current situation to see if we're a good fit. 

Before we dive into a demo, I'd like to understand your specific challenges and goals. Is that okay with you?"

### Discovery Questions by Category

#### Metrics & Pain Discovery
1. "Tell me about the manual processes your team handles daily."
2. "How much time would you estimate your team spends on repetitive tasks each week?"
3. "What happens when these manual processes slow down or fail?"
4. "How do you currently measure team productivity?"
5. "What would success look like if we could automate these processes?"

#### Decision Process Discovery
1. "Have you looked at automation solutions before?"
2. "Who else is involved in evaluating new tools like this?"
3. "What's your typical process for choosing new software?"
4. "What's your timeline for making a decision?"
5. "What budget range are you working with for operational efficiency tools?"

#### Champion & Stakeholder Discovery
1. "Who would benefit most from these automations?"
2. "Who currently feels the pain of these manual processes?"
3. "Who would I need to speak with to move this forward?"
4. "How do you typically get buy-in for new initiatives?"

### Closing the Qualification Call
Based on MEDDIC-Lite score:

#### High/Well Qualified
"Based on our conversation, it sounds like RP9 could be a great fit for your needs. I'd like to show you exactly how we can solve [specific pain points] and deliver [specific metrics]. When would be a good time for a customized demo?"

#### Moderate Qualification
"I see some great opportunities for RP9 to help your team. I'd like to dig deeper into [qualification gap] and show you some relevant examples. Would you be interested in attending our upcoming webinar on [relevant topic]?"

#### Poor Qualification
"Thank you for sharing your situation. While RP9 might not be the right fit at this moment, I'd like to keep you informed about our solutions. Would it be okay if I add you to our newsletter and check back in a few months?"

---

## Deal Risk Assessment

### Green Flags (Proceed with Confidence)
- ✅ Strong MEDDIC-Lite score (20+)
- ✅ Clear, quantifiable pain points
- ✅ Access to economic buyer
- ✅ Reasonable timeline (< 90 days)
- ✅ Budget confirmed or likely
- ✅ Strong internal champion
- ✅ Previous experience buying similar tools

### Yellow Flags (Proceed with Caution)
- ⚠️ Moderate MEDDIC-Lite score (15-19)
- ⚠️ Multiple decision makers without clear authority
- ⚠️ Extended timeline (> 90 days)
- ⚠️ Budget not confirmed
- ⚠️ Evaluating multiple vendors
- ⚠️ New to automation/software purchases

### Red Flags (High Risk or Disqualify)
- 🚩 Low MEDDIC-Lite score (< 15)
- 🚩 No clear pain or urgency
- 🚩 Price shopping without business case
- 🚩 No access to decision makers
- 🚩 Unrealistic expectations
- 🚩 Bad fit for our ideal customer profile
- 🚩 Complicated approval process
- 🚩 Previous bad experience with automation

---

## Next Steps After Qualification

### For Qualified Prospects
1. **Schedule Demo** within 1-2 weeks
2. **Prepare Custom Content** (ROI analysis, relevant case studies)
3. **Research Their Industry** and specific use cases
4. **Plan Demo Flow** based on their priority pain points
5. **Set Clear Next Steps** and timeline expectations

### For Nurture Prospects
1. **Add to Nurture Campaign** appropriate for their stage
2. **Schedule Follow-up** based on their timeline
3. **Send Relevant Content** (case studies, ROI calculators)
4. **Monitor Engagement** and adjust approach
5. **Re-qualify Periodically** as situation may change

---

## Common Objections and Responses

### "We don't have budget right now"
- "I understand budget timing can be challenging. Help me understand - if you could show a clear ROI within 3-6 months, would that change the budget conversation?"
- "What would need to happen for budget to become available?"
- "Many of our clients actually use the cost savings from automation to fund the investment. Would you like to see how that might work?"

### "We need to evaluate other options"
- "That makes complete sense. What other solutions are you looking at?"
- "What criteria are most important in your evaluation?"
- "I'd love to understand how we compare on your key requirements. Could we schedule a brief comparison discussion?"

### "This seems too complex for our team"
- "I hear that concern often. Most of our clients are surprised by how simple implementation can be. Would you like to see how [similar company] got started?"
- "What if we could start with just one simple automation to prove the concept?"
- "Our average implementation time is just 2 weeks. What part seems most complex to you?"

### "We're too small for this"
- "Actually, smaller teams often see the biggest impact because automation frees up a higher percentage of their time."
- "We have several clients your size who are saving [specific amount]. Would you like to hear their story?"
- "Our Starter plan is specifically designed for teams like yours."

---

## Tools and Resources

### Qualification Tools
- [ ] **MEDDIC-Lite Scorecard** (internal scoring sheet)
- [ ] **ROI Calculator** (customize based on their metrics)
- [ ] **Champion Identification Guide** (help identify internal supporters)
- [ ] **Decision Process Mapper** (visualize their buying process)

### Content Assets
- [ ] **Industry-Specific Case Studies**
- [ ] **ROI Analysis Templates**
- [ ] **Implementation Timeline Examples**
- [ ] **Reference Customer Contacts**

### Follow-up Templates
- [ ] **Qualification Follow-up Email**
- [ ] **Demo Scheduling Email**
- [ ] **Nurture Email Sequences**
- [ ] **Stakeholder Introduction Email**

---

*This playbook should be reviewed and updated quarterly based on sales team feedback and win/loss analysis.*