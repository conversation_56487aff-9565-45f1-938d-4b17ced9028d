# RP9 Portal Environment Variables (Phase 0)

# n8n Configuration (Required for Phase 0)
N8N_BASE_URL=https://primary-production-7f25.up.railway.app
N8N_API_KEY=REPLACE_WITH_YOUR_N8N_API_KEY

# Security Configuration (Phase 0)
HMAC_SECRET=your-super-secure-hmac-secret-change-in-production
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production

# Frontend Configuration
FRONTEND_URL=http://localhost:3000

# Database Configuration (Supabase)
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU

# Stripe Configuration
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# App Configuration
NEXT_PUBLIC_APP_NAME=RP9 Portal
NEXT_PUBLIC_APP_VERSION=1.0.0

# Development
NODE_ENV=development

# Railway n8n Configuration Instructions (Phase 0)
# 1. Copy the N8N_BASE_URL from your Railway n8n deployment
# 2. Generate an API key from your n8n instance: Settings > API > Generate API Key  
# 3. Replace N8N_API_KEY with your generated key
# 4. Set HMAC_SECRET for secure webhooks

# === Phase 4: Security, SRE & Compliance ===

# Observabilidad / Alertas
ALERTS_SLACK_WEBHOOK=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK
STATUSPAGE_API_KEY=your-status-page-api-key
GRAFANA_REMOTE_WRITE_URL=https://prometheus-prod-10-prod-us-central-0.grafana.net/api/prom/push
GRAFANA_REMOTE_WRITE_TOKEN=your-grafana-cloud-token

# Backups & Recovery
BACKUPS_BUCKET=supabase://backups
BACKUPS_ENCRYPTION_KEY=your-backup-encryption-key-32-chars-min
BACKUP_RETENTION_DAYS=14

# Seguridad / Perímetro
CLOUDFLARE_ZONE_ID=your-cloudflare-zone-id
CLOUDFLARE_API_TOKEN=your-cloudflare-api-token
REQUIRE_2FA=true
IP_ALLOWLIST_ENABLED=false

# Health Monitoring
HEALTH_CHECK_TIMEOUT=10000
ALERT_ESCALATION_MINUTES=30

# Audit & Compliance
AUDIT_LOG_RETENTION_DAYS=90
DATA_RETENTION_POLICY_DAYS=60
ANONYMIZATION_ENABLED=false

# Feature Flags for Phase 4
FF_ENHANCED_SECURITY=true
FF_BACKUP_MONITORING=true
FF_AUDIT_TRAIL=true
FF_IP_ALLOWLIST=false

# === Phase 5: Onboarding & Time-to-Value ===

# Onboarding Configuration
ONBOARDING_WIZARD_ENABLED=true
TEMPLATE_INSTALLATION_TIMEOUT=30000
DEFAULT_TEMPLATE_COUNTRY=MX
ONBOARDING_PROGRESS_TRACKING=true

# Health Score System
HEALTH_SCORE_UPDATE_INTERVAL=300000
ACTIVATION_THRESHOLD_SCORE=70
ACTIVATION_MIN_EXECUTIONS=5
ACTIVATION_MIN_OUTCOMES=1
HEALTH_SCORE_WEIGHTS_OUTCOME=0.5
HEALTH_SCORE_WEIGHTS_INTEGRATION=0.3
HEALTH_SCORE_WEIGHTS_USAGE=0.2

# Notification & Digest System
DIGEST_NOTIFICATION_ENABLED=true
DIGEST_SEND_DAYS=1,3,7,14
EMAIL_NOTIFICATIONS_ENABLED=false
WHATSAPP_NOTIFICATIONS_ENABLED=false
NOTIFICATION_BATCH_SIZE=50
NOTIFICATION_RETRY_ATTEMPTS=3

# Evidence Upload System
EVIDENCE_UPLOAD_MAX_SIZE=10485760
EVIDENCE_STORAGE_BUCKET=evidence
EVIDENCE_ALLOWED_TYPES=image/jpeg,image/png,application/pdf,video/mp4
EVIDENCE_CLEANUP_DAYS=90

# Template Catalog
TEMPLATE_CACHE_TTL=3600
COUNTRY_ORDERING_ENABLED=true
TEMPLATE_EXECUTION_TIMEOUT=60000

# Gamification System
ACHIEVEMENTS_ENABLED=true
LEVEL_SYSTEM_ENABLED=true
PROGRESS_NOTIFICATIONS_ENABLED=true

# Geographic Detection
GEO_DETECTION_ENABLED=true
GEO_DEFAULT_COUNTRY=MX
SUPPORTED_COUNTRIES=MX,CO,CL,AR,PE,DO,CR,GT,HN,SV,NI,PA,UY,PY,BO,EC,VE

# Real-time Features
REALTIME_PROGRESS_UPDATES=true
WEBSOCKET_ENABLED=false
POLLING_INTERVAL=30000

# Feature Flags for Phase 5
FF_ONBOARDING_WIZARD=true
FF_HEALTH_SCORE=true
FF_GAMIFICATION=true
FF_EVIDENCE_UPLOAD=true
FF_DIGEST_NOTIFICATIONS=true
FF_TEMPLATE_CATALOG=true
FF_GEO_DETECTION=true

# === Phase 10: Soporte, SLAs & Customer Success ===

# HubSpot Service Hub (Tickets)
HUBSPOT_PRIVATE_APP_TOKEN=prv-your-hubspot-private-app-token
HUBSPOT_PORTAL_ID=your-hubspot-portal-id
HUBSPOT_WEBHOOK_SECRET=your-hubspot-webhook-secret

# Slack Notifications
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/CS/WEBHOOK
SLACK_CS_CHANNEL=#customer-success
SLACK_SUPPORT_CHANNEL=#support

# Status Page Providers (choose one)
STATUS_PROVIDER=statuspage
# For Statuspage.io:
STATUSPAGE_API_TOKEN=your-statuspage-oauth-token
STATUSPAGE_PAGE_ID=your-statuspage-page-id
# For BetterStack:
BETTERSTACK_API_TOKEN=your-betterstack-api-token

# NPS/CSAT Survey Providers (stubs for now)
EMAIL_PROVIDER_API_KEY=your-email-provider-api-key
WHATSAPP_PROVIDER_TOKEN=your-whatsapp-provider-token

# Customer Success Configuration
CS_HEALTH_SCORE_UPDATE_FREQUENCY=daily
CS_QBR_AUTO_SCHEDULE=true
CS_RENEWAL_REMINDER_DAYS=60,30,15
CS_DUNNING_ATTEMPT_LIMIT=3
CS_ESCALATION_ENABLED=true

# SLA Configuration
SLA_P1_FRT_MINUTES=60
SLA_P1_RESTORE_MINUTES=120
SLA_P2_FRT_MINUTES=240
SLA_P2_RESTORE_MINUTES=480
SLA_P3_FRT_MINUTES=480
SLA_P3_RESTORE_MINUTES=2880

# Support Portal Configuration
SUPPORT_BUSINESS_HOURS_START=09:00
SUPPORT_BUSINESS_HOURS_END=17:00
SUPPORT_TIMEZONE=America/Mexico_City
SUPPORT_WEEKDAYS=1,2,3,4,5
SUPPORT_KB_FEEDBACK_ENABLED=true
SUPPORT_RATE_LIMIT_PER_HOUR=10

# Feature Requests Portal
FEATURE_REQUESTS_PUBLIC_ENABLED=true
FEATURE_REQUESTS_VOTING_ENABLED=true
FEATURE_REQUESTS_ANONYMOUS_ENABLED=false

# Health Score Weights (must sum to 1.0)
HEALTH_SCORE_WEIGHT_USAGE=0.30
HEALTH_SCORE_WEIGHT_SUCCESS=0.25
HEALTH_SCORE_WEIGHT_INCIDENTS=0.20
HEALTH_SCORE_WEIGHT_NPS=0.15
HEALTH_SCORE_WEIGHT_ENGAGEMENT=0.10

# Feature Flags for Phase 10
FF_SUPPORT_SYSTEM=true
FF_HEALTH_SCORE_CS=true
FF_QBR_SCHEDULING=true
FF_INCIDENT_MANAGEMENT=true
FF_KB_FEEDBACK=true
FF_FEATURE_REQUESTS=true
FF_NPS_SURVEYS=true
FF_RENEWAL_DUNNING=true

# === Phase 11: Analytics & Reporting ===

# Analytics Backend Configuration
ANALYTICS_ENABLED=true
ANALYTICS_WEBHOOK_SECRET=your-analytics-webhook-secret-change-in-production
NEXT_PUBLIC_ANALYTICS_WEBHOOK_SECRET=your-analytics-webhook-secret-public
INTERNAL_API_KEY=your-internal-api-key-for-scheduled-functions

# Data Collection Configuration
N8N_WEBHOOK_URL=https://primary-production-7f25.up.railway.app/webhook
N8N_API_KEY=your-n8n-api-key-for-data-collection
DATA_COLLECTION_INTERVAL_MINUTES=15
DATA_AGGREGATION_TIME=02:00

# Alerting & Monitoring
SLACK_ALERTS_WEBHOOK=https://hooks.slack.com/services/YOUR/ANALYTICS/WEBHOOK
ANALYTICS_ALERT_THRESHOLD_CRITICAL=true
ANALYTICS_ALERT_THRESHOLD_HIGH=true
ANALYTICS_ALERT_THRESHOLD_MEDIUM=false
ANALYTICS_ALERT_SUMMARY_ENABLED=true

# ROI & Business Metrics Configuration
DEFAULT_HOURLY_RATE_USD=50
ROI_CALCULATION_METHOD=hours_saved_minus_costs
TTV_TARGET_DAYS=7
ADOPTION_TARGET_PERCENTAGE=75
SUCCESS_RATE_SLA=95

# Data Retention & Cleanup
ANALYTICS_DATA_RETENTION_MONTHS=6
MATERIALIZED_VIEW_REFRESH_FREQUENCY=daily
RAW_DATA_CLEANUP_ENABLED=true
DATA_QUALITY_MONITORING=true

# Export & Reporting
REPORTS_PDF_ENABLED=true
REPORTS_CSV_EXPORT_ENABLED=true
REPORTS_SCHEDULE_MONTHLY=true
REPORTS_MAX_EXPORT_RECORDS=10000
REPORTS_CACHE_TTL_MINUTES=300

# North Star Metric Configuration
NORTH_STAR_METRIC=roi_usd_monthly
NSM_TARGET_ENTERPRISE=2000
NSM_TARGET_PROFESSIONAL=500
NSM_TARGET_STARTER=100

# Dashboard Configuration
DASHBOARD_CACHE_TTL_SECONDS=300
DASHBOARD_REALTIME_UPDATES=false
DASHBOARD_AUTO_REFRESH_INTERVAL=60

# Anomaly Detection
ANOMALY_DETECTION_ENABLED=true
ANOMALY_STD_DEV_THRESHOLD=2
ANOMALY_LOOKBACK_DAYS=7
ANOMALY_MIN_DATA_POINTS=10

# Performance Optimization
ANALYTICS_QUERY_TIMEOUT_MS=30000
MATERIALIZED_VIEW_PARALLEL_REFRESH=true
DATABASE_CONNECTION_POOL_SIZE=10

# Security & Access Control
ANALYTICS_API_RATE_LIMIT_PER_MINUTE=100
ANALYTICS_EXPORT_RATE_LIMIT_PER_MINUTE=5
ANALYTICS_AUDIT_LOG_ENABLED=true
TENANT_DATA_ISOLATION_STRICT=true

# Feature Flags for Phase 11
FF_ANALYTICS_DASHBOARDS=true
FF_ANALYTICS_ALERTS=true
FF_ANALYTICS_EXPORTS=true
FF_ANALYTICS_REPORTS=true
FF_ANOMALY_DETECTION=true
FF_ROI_TRACKING=true
FF_TTV_COHORTS=true
FF_FUNNEL_ANALYSIS=true

# === Phase 12: Marketplace & Plantillas Monetizadas ===

# Stripe Connect Configuration
STRIPE_CONNECT_APP_FEE_BPS=3000
STRIPE_PAYOUT_THRESHOLD_USD=50
STRIPE_CONNECT_WEBHOOK_SECRET=whsec_your_connect_webhook_secret

# Marketplace Core Settings
MARKETPLACE_ENABLED=true
MARKETPLACE_REVENUE_SHARE_DEFAULT_BPS=7000
MARKETPLACE_CURATION_ENABLED=true
MARKETPLACE_AUTO_APPROVE_TRUSTED=false

# Preview System Configuration
FREE_PREVIEW_EXECUTIONS=5
PREVIEW_TOKEN_DURATION_HOURS=24
PREVIEW_RATE_LIMIT_PER_HOUR=20
PREVIEW_MOCK_MODE_DEFAULT=true

# Template Installation Settings
TEMPLATE_INSTALL_TIMEOUT_MS=30000
TEMPLATE_AUTO_UPDATE_MINORS=true
TEMPLATE_UPDATE_CHECK_INTERVAL_HOURS=24
TEMPLATE_BACKUP_BEFORE_UPDATE=true

# Creator Onboarding
CREATOR_ONBOARDING_ENABLED=true
CREATOR_KYC_REQUIRED=true
CREATOR_MIN_PAYOUT_USD=10
CREATOR_PROFILE_REQUIRED_FIELDS=display_name,country

# Quality Control & Security
TEMPLATE_LINTING_ENABLED=true
TEMPLATE_SECURITY_SCAN_ENABLED=true
TEMPLATE_APPROVAL_REQUIRED=true
TEMPLATE_CONTENT_MODERATION=true

# Pricing & Monetization
MARKETPLACE_SUPPORT_ONE_OFF_PRICING=true
MARKETPLACE_SUPPORT_SUBSCRIPTION_PRICING=true
MARKETPLACE_BUNDLE_DISCOUNT_ENABLED=true
MARKETPLACE_FREE_TEMPLATES_ENABLED=true

# Search & Discovery
MARKETPLACE_SEARCH_ENABLED=true
MARKETPLACE_CATEGORIES_ENABLED=true
MARKETPLACE_FEATURED_ALGORITHM=true
MARKETPLACE_RELATED_ITEMS_COUNT=4

# Analytics & Metrics
MARKETPLACE_ANALYTICS_ENABLED=true
MARKETPLACE_TRACK_VIEWS=true
MARKETPLACE_TRACK_CONVERSIONS=true
MARKETPLACE_ADOPTION_SCORING=true

# Refunds & Support  
MARKETPLACE_REFUNDS_ENABLED=true
MARKETPLACE_REFUND_WINDOW_DAYS=7
MARKETPLACE_REFUND_DEFAULT_TYPE=credit
MARKETPLACE_SUPPORT_ENABLED=true

# Alerts & Notifications
MARKETPLACE_SLACK_WEBHOOKS=true
MARKETPLACE_EMAIL_NOTIFICATIONS=true
MARKETPLACE_CREATOR_NOTIFICATIONS=true
MARKETPLACE_ADMIN_NOTIFICATIONS=true

# Performance & Caching
MARKETPLACE_CACHE_TTL_SECONDS=300
MARKETPLACE_LIST_PAGE_SIZE_DEFAULT=20
MARKETPLACE_LIST_PAGE_SIZE_MAX=100
MARKETPLACE_IMAGE_OPTIMIZATION=true

# File Storage & CDN
MARKETPLACE_STORAGE_BUCKET=marketplace-assets
MARKETPLACE_JSON_STORAGE_BUCKET=templates
MARKETPLACE_CDN_ENABLED=false
MARKETPLACE_MAX_TEMPLATE_SIZE_MB=5

# Localization
MARKETPLACE_DEFAULT_CURRENCY=usd
MARKETPLACE_SUPPORTED_CURRENCIES=usd,mxn,cop,clp
MARKETPLACE_DEFAULT_LOCALE=es
MARKETPLACE_SUPPORTED_LOCALES=es,en

# Rate Limiting
MARKETPLACE_API_RATE_LIMIT_PER_MINUTE=100
MARKETPLACE_PREVIEW_RATE_LIMIT_PER_HOUR=20
MARKETPLACE_PURCHASE_RATE_LIMIT_PER_HOUR=10
MARKETPLACE_INSTALL_RATE_LIMIT_PER_HOUR=30

# Featured Algorithm Weights
FEATURED_WEIGHT_ADOPTION=0.25
FEATURED_WEIGHT_ENGAGEMENT=0.20
FEATURED_WEIGHT_QUALITY=0.20
FEATURED_WEIGHT_PERFORMANCE=0.15
FEATURED_WEIGHT_MONETIZATION=0.10
FEATURED_WEIGHT_FRESHNESS=0.10

# Payout Configuration
PAYOUT_SCHEDULE=monthly
PAYOUT_DAY_OF_MONTH=15
PAYOUT_MINIMUM_THRESHOLD_USD=50
PAYOUT_CURRENCY_CONVERSION=true
PAYOUT_CSV_REPORTS=true
PAYOUT_SLACK_NOTIFICATIONS=true

# Content Guidelines
TEMPLATE_MIN_TITLE_LENGTH=10
TEMPLATE_MAX_TITLE_LENGTH=100
TEMPLATE_MIN_DESCRIPTION_LENGTH=50
TEMPLATE_MAX_DESCRIPTION_LENGTH=500
TEMPLATE_MAX_TAGS=10
TEMPLATE_REQUIRED_TAGS=category,difficulty

# Security & Compliance
MARKETPLACE_HMAC_WEBHOOK_VALIDATION=true
MARKETPLACE_API_KEY_VALIDATION=true
MARKETPLACE_RLS_ENABLED=true
MARKETPLACE_AUDIT_LOG_ENABLED=true

# Feature Flags for Phase 12
FF_MARKETPLACE_ENABLED=true
FF_CREATOR_PORTAL=true
FF_TEMPLATE_PREVIEWS=true
FF_STRIPE_CONNECT=true
FF_AUTO_PAYOUTS=true
FF_FEATURED_ALGORITHM=true
FF_TEMPLATE_REVIEWS=true
FF_MARKETPLACE_ANALYTICS=true
FF_BUNDLE_PRICING=true
FF_TEMPLATE_UPDATES=true

# Development & Testing
MARKETPLACE_DEV_MODE=false
MARKETPLACE_MOCK_PAYMENTS=false
MARKETPLACE_BYPASS_KYC=false
MARKETPLACE_TEST_CREATOR_MODE=false

# === Fase 13: Orchestrator Multi-tenancy ===

# Orchestrator VPS Configuration
ORCHESTRATOR_BASE_URL=https://orchestrator.rp9.io
ORCHESTRATOR_JWT_SECRET=your-orchestrator-jwt-secret-min-32-chars
ORCHESTRATOR_HMAC_SECRET=your-orchestrator-hmac-secret-min-32-chars

# Multi-tenancy Configuration
TENANCY_MODEL=hybrid
SHARED_N8N_BASE_URL=https://primary-production-7f25.up.railway.app
SHARED_N8N_API_KEY=your-shared-n8n-api-key

# Auto-scaling Thresholds
AUTOSCALE_QUEUE_WAIT_P95_THRESHOLD=5.0
AUTOSCALE_CPU_THRESHOLD=80.0
AUTOSCALE_MEMORY_THRESHOLD=85.0
AUTOSCALE_EXECUTIONS_MIN_THRESHOLD=10

# Enforcement Configuration
ENFORCEMENT_ENABLED=true
ENFORCEMENT_SYNC_WITH_STRIPE=true
ENFORCEMENT_SOFT_LIMIT_WARNINGS=true

# Promotion Triggers
PROMOTION_AUTO_ENABLED=true
PROMOTION_PERFORMANCE_TRIGGERS=true
PROMOTION_COMPLIANCE_TRIGGERS=true
PROMOTION_SCAN_INTERVAL_HOURS=4

# Docker Configuration
N8N_DOCKER_IMAGE=n8nio/n8n:latest
TRAEFIK_DOMAIN=rp9.io
ACME_EMAIL=<EMAIL>

# Backup Configuration
ORCHESTRATOR_BACKUPS_S3_BUCKET=rp9-orchestrator-backups
BACKUP_RETENTION_DAILY_DAYS=7
BACKUP_RETENTION_WEEKLY_DAYS=28
BACKUP_RETENTION_MONTHLY_DAYS=365

# Blue/Green Deployments
BLUE_GREEN_DEPLOYMENTS_ENABLED=true
N8N_UPGRADE_STRATEGY=blue_green
DEPLOYMENT_HEALTH_CHECK_TIMEOUT=300

# Observability
PROMETHEUS_ENABLED=true
GRAFANA_ENABLED=true
ORCHESTRATOR_METRICS_PORT=9090
TENANT_METRICS_COLLECTION_INTERVAL=15

# Feature Flags for Phase 13
FF_ORCHESTRATOR_ENABLED=true
FF_AUTO_SCALING=true
FF_ENFORCEMENT=true
FF_AUTO_PROMOTION=true
FF_BLUE_GREEN_DEPLOYMENTS=true
FF_S3_BACKUPS=true
FF_PROMETHEUS_METRICS=true

# === Fase 14: AI Assistant ===

# AI Service Configuration
AI_BACKEND_URL=http://localhost:3001
AI_SERVICE_JWT_SECRET=your-ai-service-jwt-secret-min-32-chars

# AI Provider Keys
OPENAI_API_KEY=sk-your-openai-api-key
ANTHROPIC_API_KEY=sk-ant-your-anthropic-api-key

# AI Model Configuration
AI_DEFAULT_MODEL=gpt-4
AI_FALLBACK_MODEL=gpt-3.5-turbo
AI_MAX_TOKENS=2000
AI_TEMPERATURE=0.7

# AI Service Features
BYOK_ENABLED=true
PII_REDACTION_ENABLED=true
AI_CACHING_ENABLED=true
AI_CACHING_TTL_SECONDS=3600

# Budget & Rate Limiting
AI_DEFAULT_MONTHLY_BUDGET_USD=20.00
AI_RATE_LIMIT_MAX=100
AI_RATE_LIMIT_WINDOW=60000

# Command Palette Configuration
COMMAND_PALETTE_ENABLED=true
COMMAND_PALETTE_SHORTCUTS_ENABLED=true

# Playground Configuration
PLAYGROUND_ENABLED=true
PLAYGROUND_TEMPLATE_SHARING=true
PLAYGROUND_MODEL_COMPARISON=true

# Security Configuration
AI_SECURITY_VALIDATION=true
AI_OUTPUT_VALIDATION=true
AI_PROMPT_INJECTION_PROTECTION=true

# Feature Flags for Phase 14
FF_AI_ASSISTANT=true
FF_COMMAND_PALETTE=true
FF_FIX_WITH_AI=true
FF_PROMPT_PLAYGROUND=true
FF_DIFF_VIEWER=true
FF_BLUEPRINT_DSL=true
FF_AI_SANDBOX=true
FF_MULTI_PROVIDER_ROUTING=true
FF_BYOK_SUPPORT=true
FF_PII_REDACTION=true