name: "Dependency Security Scan"

on:
  pull_request:
  push: 
    branches: [ main, develop ]
  schedule:
    - cron: '0 6 * * *'  # Daily at 6 AM

jobs:
  trivy-scan:
    name: Trivy Security Scan
    runs-on: ubuntu-latest
    permissions:
      security-events: write
      contents: read
      
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Setup Trivy
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: 'fs'
          scan-ref: '.'
          format: 'sarif'
          output: 'trivy-results.sarif'
          severity: 'HIGH,CRITICAL'
          scanners: 'vuln,secret,config'
          skip-dirs: 'node_modules,Arquitectura'
      
      - name: Upload Trivy scan results
        uses: github/codeql-action/upload-sarif@v3
        if: always()
        with:
          sarif_file: 'trivy-results.sarif'
          category: 'trivy'
      
      - name: Fail on critical vulnerabilities
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: 'fs'
          scan-ref: '.'
          format: 'table'
          exit-code: '1'
          severity: 'CRITICAL'
          scanners: 'vuln'
          skip-dirs: 'node_modules,Arquitectura'