# Fase 15: Internacionalización LatAm-first - Guía Completa

**RP9 Portal - Sistema de Internacionalización Completo**  
**Versión:** 1.0  
**Fecha:** 2025-01-14

## 🌍 Visión General

Fase 15 implementa un sistema completo de internacionalización (i18n) centrado en América Latina, que permite a RP9 Portal operar de manera nativa en 6 países con soporte completo para idiomas locales, monedas, métodos de pago, regulaciones fiscales y marcos legales específicos por región.

## ✨ Características Principales

### 🌎 Soporte Multi-Región
- **Países soportados**: México, Colombia, Chile, Perú, Argentina, República Dominicana + Estados Unidos
- **Idiomas**: Español (LatAm + variantes por país) + Inglés
- **Monedas**: MXN, COP, CLP, PEN, ARS, DOP, USD
- **Detección automática**: UTM > IP-geo > Accept-Language > Cookie

### 💰 Sistema de Precios Psicológicos
- **Price books dinámicos**: Precios optimizados por país y cultura de consumo
- **Toggle USD/Local**: Los usuarios pueden alternar entre moneda local y USD
- **Redondeos psicológicos**: $999 MXN, $399.000 COP, $39.900 CLP
- **Descuentos por región**: Planes anuales con ahorros localizados

### 🏛️ Cumplimiento Fiscal y Legal
- **Identificación fiscal**: RFC (MX), NIT (CO), RUT (CL), RUC (PE), CUIT (AR), RNC (DO)
- **Validación automática**: Algoritmos de validación específicos por país
- **Modalidad de impuestos**: Gross (B2C) vs Net (B2B) según regulación local
- **Plantillas legales**: Términos y condiciones + anexos específicos por país

### 🎛️ Feature Flags Inteligentes
- **Por país**: Métodos de pago locales, funciones de compliance
- **Por tenant**: Configuraciones empresariales específicas
- **Experimentación**: A/B testing de funciones por región

### 📊 Analytics Multi-Moneda
- **Normalización USD**: Para comparación y reportes ejecutivos
- **Display local**: UI siempre en moneda del usuario
- **Conversion rates**: Tipos de cambio actualizables dinámicamente

## 🚀 Cómo Usar

### Para Desarrolladores

#### 1. Configuración Inicial

```bash
# Instalar dependencias (ya incluidas)
npm install next-intl @supabase/supabase-js stripe

# Ejecutar migraciones de base de datos
npm run migrate-templates

# Exportar traducciones desde Supabase
npm run export-i18n

# Para un idioma específico
npm run export-i18n:locale es-MX
```

#### 2. Uso de Componentes

```tsx
import { PriceTag } from '@/components/billing/PriceTag'
import { CurrencyToggle } from '@/components/billing/CurrencyToggle'
import { TaxIdField } from '@/components/billing/TaxIdField'

function PricingPage() {
  const [currency, setCurrency] = useState<'LOCAL' | 'USD'>('LOCAL')
  
  return (
    <div>
      {/* Toggle entre monedas */}
      <CurrencyToggle 
        onCurrencyChange={setCurrency}
        variant="button"
      />
      
      {/* Mostrar precio con equivalente */}
      <PriceTag 
        priceData={{
          localPrice: 999,
          localCurrency: 'MXN',
          usdPrice: 49.95
        }}
        showToggle={true}
        variant="card"
      />
      
      {/* Campo de identificación fiscal */}
      <TaxIdField 
        country="MX"
        businessType="B2B"
        required={true}
      />
    </div>
  )
}
```

#### 3. Hooks para Feature Flags

```tsx
import { useFeatureFlags, usePaymentMethods } from '@/lib/hooks/useFeatureFlags'

function CheckoutPage() {
  const { hasFeature } = useFeatureFlags('tenant_123')
  const { isMethodEnabled } = usePaymentMethods()
  
  return (
    <div>
      {/* Mostrar OXXO solo si está habilitado */}
      {isMethodEnabled('oxxo') && (
        <PaymentMethod method="oxxo" />
      )}
      
      {/* Funciones experimentales */}
      {hasFeature('experimental.ai_recommendations') && (
        <AIRecommendations />
      )}
    </div>
  )
}
```

#### 4. Analytics Multi-Moneda

```tsx
import { useMultiCurrencyAnalytics } from '@/lib/analytics/currency'

function DashboardAnalytics() {
  const { formatCurrency, formatInUSD, analytics } = useMultiCurrencyAnalytics()
  
  const amounts = [
    { amount: 999, currency: 'MXN', usdAmount: 49.95 },
    { amount: 199000, currency: 'COP', usdAmount: 49.75 }
  ]
  
  const summary = analytics.calculateRevenueSummary(amounts)
  
  return (
    <div>
      <h3>Revenue Total: {summary.totalUSDFormatted}</h3>
      {summary.currencies.map(curr => (
        <div key={curr.currency}>
          {curr.formatted} ({curr.percentage.toFixed(1)}%)
        </div>
      ))}
    </div>
  )
}
```

### Para Administradores

#### 1. Gestión de Traducciones

1. **Acceder al Admin Panel**: `/admin/i18n`
2. **Seleccionar idioma** del dropdown
3. **Filtrar por namespace** (billing, common, etc.)
4. **Editar mensajes** inline con validación en tiempo real
5. **Exportar JSON** para desarrollo local

#### 2. Configuración de Precios

Los precios se configuran en `/config/pricebook.json` y se sincronizan con Supabase:

```json
{
  "plans": {
    "starter": {
      "pricing": {
        "MX": {
          "monthly": {
            "stripe_price_id": "price_mx_starter_monthly",
            "currency": "MXN",
            "psychological_price": 999.00,
            "usd_equivalent": 49.95
          }
        }
      }
    }
  }
}
```

#### 3. Feature Flags por País

Configurar en Supabase tabla `country_feature_flags`:

```sql
INSERT INTO country_feature_flags (country_code, payment_methods, billing_features) 
VALUES (
  'MX', 
  '["card", "oxxo"]',
  '{"dunning": true, "autopay": true}'
);
```

### Para Usuarios Finales

#### Experiencia Automática
1. **Detección automática**: El sistema detecta tu país y configura el idioma y moneda
2. **Toggle de moneda**: Puedes alternar entre tu moneda local y USD
3. **Precios localizados**: Los precios se muestran con redondeos psicológicos familiares
4. **Métodos de pago locales**: OXXO (México), PSE (Colombia), etc.
5. **Compliance automático**: Captura automática de datos fiscales según tu país

#### URLs Regionales
- `rp9.io/es-MX/pricing` - Precios en pesos mexicanos
- `rp9.io/mx/pricing` - Alias corto para México  
- `rp9.io/co/pricing` - Colombia con precios en COP
- `rp9.io/en-US/pricing` - Versión en inglés con USD

## 💡 Ventajas del Sistema

### 🎯 Para el Negocio

1. **Conversión mejorada**: Precios psicológicos aumentan conversión 15-30%
2. **Reducción de fricción**: Checkout nativo sin conversiones manuales
3. **Compliance automático**: Reduce riesgo legal y operativo
4. **Expansión acelerada**: Framework escalable para nuevos países
5. **Analytics unificadas**: Comparación cross-región en USD normalizado

### 👥 Para los Usuarios

1. **Experiencia nativa**: Todo en su idioma y moneda local
2. **Confianza aumentada**: Precios claros sin sorpresas de cambio
3. **Métodos familiares**: Pago con opciones que conocen y confían
4. **Cumplimiento simplificado**: Tax ID y facturación automática
5. **Soporte localizado**: Términos legales en su jurisdicción

### ⚙️ Para el Equipo Técnico

1. **Mantenimiento centralizado**: Una base de código para todas las regiones
2. **Configuración dinámica**: Cambios de precio y features sin deployments
3. **Testing robusto**: Suite de tests para validación multi-región
4. **Observabilidad completa**: Métricas normalizadas y por región
5. **Escalabilidad inherente**: Agregar países es configuración, no código

## 📋 Casos de Uso Comunes

### Caso 1: Usuario Mexicano Comprando Plan Pro
1. **Detección**: IP mexicana → locale es-MX → currency MXN
2. **Pricing**: Muestra $1,999 MXN/mes (≈ $99.95 USD)
3. **Checkout**: Ofrece tarjeta + OXXO, requiere RFC
4. **Facturación**: CFDI automático con IVA incluido
5. **Legal**: Términos con anexo específico para México

### Caso 2: Empresa Chilena (B2B) Plan Enterprise
1. **Detección**: Campaña UTM → locale es-CL → currency CLP  
2. **Pricing**: $199.900 CLP/mes (pricing net, sin IVA)
3. **Tax ID**: Validación automática de RUT chileno
4. **Checkout**: Precios + IVA, requiere razón social
5. **Compliance**: Integración con SII para facturación electrónica

### Caso 3: Analytics Cross-Región
1. **Data Collection**: Revenue en MXN, COP, CLP, etc.
2. **Normalization**: Todo convertido a USD para comparación
3. **Display**: CEO ve $50K total, CFO México ve $1M MXN
4. **Insights**: "Argentina tiene 25% más ARPU que Colombia"
5. **Decisions**: Inversión en marketing basada en ROI por país

## 🔧 Configuración Avanzada

### Variables de Entorno

```env
# Next.js i18n
NEXT_PUBLIC_DEFAULT_LOCALE=es-419
NEXT_PUBLIC_SUPPORTED_LOCALES=es-419,es-MX,es-CO,es-CL,es-PE,es-AR,es-DO,en-US

# Supabase
SUPABASE_URL=your_supabase_url
SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_key

# Stripe
STRIPE_SECRET_KEY=sk_test_or_live_key
STRIPE_WEBHOOK_SECRET=whsec_webhook_secret

# Features
ENABLE_FEATURE_FLAGS=true
ENABLE_MULTI_CURRENCY=true
```

### Estructura de Base de Datos

Las tablas clave son:
- `country_configs`: Configuración base por país
- `price_books`: Precios por país/plan/período  
- `country_feature_flags`: Feature flags por país
- `tenant_feature_flags`: Overrides por tenant
- `i18n_messages`: Traducciones gestionadas
- `billing_events`: Analytics de checkout

### Personalización por Cliente Enterprise

```sql
-- Habilitar pagos en crypto para tenant específico
INSERT INTO tenant_feature_flags (tenant_id, experimental) 
VALUES ('enterprise_tenant_123', '{"payment_crypto": true}');

-- Pricing personalizado para volumen
INSERT INTO price_books (country_code, plan_id, tenant_id, psychological_price)
VALUES ('MX', 'enterprise', 'big_corp_456', 3999.00);
```

## 🧪 Testing

### Ejecutar Tests

```bash
# Tests unitarios
npm run test src/lib/__tests__/i18n-currency.test.ts

# Tests E2E de checkout
npm run test src/lib/__tests__/checkout-e2e.test.ts

# Coverage completo
npm run test:coverage
```

### Testing Manual por País

1. **Cambiar locale**: Agregar `?locale=es-MX` a URL
2. **Simular IP**: Headers `x-country: MX`
3. **Test checkout**: Diferentes países y métodos de pago
4. **Validar analytics**: Verificar normalización USD

## 📈 Métricas y KPIs

### Métricas de Adopción
- **Conversion Rate por país**: Meta +25% vs USD-only
- **Bounce rate en pricing**: Meta -30% con precios locales
- **Payment success rate**: Meta +40% con métodos locales
- **Customer satisfaction**: Meta 4.5+ stars por país

### Métricas Técnicas
- **Page load time**: <2s para detección de locale
- **API response time**: <500ms para price lookups
- **Error rate**: <1% en conversión de monedas
- **Uptime**: 99.9% para funciones críticas

### Métricas de Negocio
- **Revenue per region**: Normalizado en USD
- **ARPU by country**: Comparación local vs USD pricing
- **Market penetration**: % de target addressable market
- **LTV/CAC ratio**: Por país y canal de adquisición

## 🔄 Roadmap Futuro

### Fase 15.1 (Q2 2025): Métodos de Pago Locales
- **México**: OXXO, SPEI, Banorte
- **Colombia**: PSE, Bancolombia, Efecty
- **Chile**: Khipu, Webpay, Redcompra
- **Argentina**: MercadoPago, Rapipago, PagoFácil

### Fase 15.2 (Q3 2025): Compliance Avanzado
- **México**: CFDI 4.0, Portal SAT
- **Colombia**: Facturación electrónica DIAN
- **Chile**: Integración SII, DTE
- **Argentina**: Factura electrónica AFIP

### Fase 15.3 (Q4 2025): Nuevos Mercados
- **Brasil**: Português, BRL, PIX
- **Ecuador**: USD, transferencias bancarias
- **Uruguay**: UYU, RedPagos
- **España**: EUR, SEPA

## 🆘 Troubleshooting

### Problemas Comunes

**1. Precios no se muestran en moneda local**
```bash
# Verificar configuración de país
curl "/api/pricebook?country=MX&plan=starter&period=monthly"

# Revisar middleware de detección
grep -r "x-country" logs/
```

**2. Traducciones no aparecen**
```bash
# Exportar desde Supabase
npm run export-i18n es-MX

# Verificar archivos generados
ls -la src/i18n/messages/
```

**3. Feature flags no funcionan**
```sql
-- Verificar configuración
SELECT * FROM country_feature_flags WHERE country_code = 'MX';
SELECT * FROM tenant_feature_flags WHERE tenant_id = 'your_tenant';
```

### Logs Útiles

```bash
# Middleware de detección
tail -f logs/middleware.log | grep "locale-detection"

# Checkout y pricing
tail -f logs/netlify-functions.log | grep "billing-checkout"

# Analytics y conversión
tail -f logs/analytics.log | grep "currency-conversion"
```

## 📞 Soporte

### Documentación Técnica
- **API Reference**: `/docs/api`
- **Component Library**: `/docs/components`  
- **Database Schema**: `/docs/schema`

### Contacto
- **Tech Lead**: <EMAIL>
- **Product**: <EMAIL>
- **DevOps**: <EMAIL>

---

**¡Fase 15 está lista para impulsar la expansión global de RP9 Portal! 🚀**

*Esta implementación establece las bases para un crecimiento sostenible en América Latina con la experiencia de usuario más nativa posible en cada país.*