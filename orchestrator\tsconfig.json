{"compilerOptions": {"target": "ES2022", "module": "CommonJS", "lib": ["ES2022"], "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": false, "resolveJsonModule": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "allowSyntheticDefaultImports": true, "moduleResolution": "node", "typeRoots": ["./node_modules/@types", "./src/types"], "baseUrl": "./src", "paths": {"@/*": ["./*"], "@/types/*": ["./types/*"], "@/services/*": ["./services/*"], "@/utils/*": ["./utils/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts"]}