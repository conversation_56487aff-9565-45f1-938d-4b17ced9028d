{"name": "@rp9/ai-service", "version": "1.0.0", "description": "AI Assistant Service for RP9 - Workflow generation, error analysis & optimization", "main": "dist/index.js", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/**/*.ts", "type-check": "tsc --noEmit"}, "dependencies": {"fastify": "^4.24.3", "@fastify/cors": "^8.4.2", "@fastify/rate-limit": "^9.1.0", "@fastify/helmet": "^11.1.1", "@supabase/supabase-js": "^2.39.0", "zod": "^3.22.4", "pino": "^8.17.2", "pino-pretty": "^10.3.1", "lru-cache": "^10.1.0", "crypto": "^1.0.1", "node-fetch": "^3.3.2"}, "devDependencies": {"typescript": "^5.3.3", "tsx": "^4.6.2", "@types/node": "^20.10.5", "jest": "^29.7.0", "@types/jest": "^29.5.8", "ts-jest": "^29.1.1", "eslint": "^8.56.0", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0"}, "keywords": ["ai", "assistant", "n8n", "workflow", "automation", "rp9"], "author": "RP9", "license": "MIT"}