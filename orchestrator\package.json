{"name": "rp9-orchestrator", "version": "1.0.0", "description": "RP9 Multi-tenancy Orchestrator Service", "main": "dist/index.js", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "lint": "eslint src/**/*.ts", "test": "jest", "docker:build": "docker build -t rp9-orchestrator .", "docker:run": "docker run -p 8080:8080 --env-file .env rp9-orchestrator"}, "keywords": ["orchestrator", "multi-tenancy", "docker", "n8n", "traefik"], "author": "RP9 Platform Team", "license": "MIT", "dependencies": {"fastify": "^4.24.3", "@fastify/cors": "^8.4.0", "@fastify/helmet": "^11.1.1", "@fastify/rate-limit": "^8.0.3", "@fastify/auth": "^4.4.0", "dockerode": "^4.0.0", "pino": "^8.16.2", "pino-pretty": "^10.2.3", "zod": "^3.22.4", "jsonwebtoken": "^9.0.2", "crypto": "^1.0.1", "node-cron": "^3.0.3", "@supabase/supabase-js": "^2.38.4", "stripe": "^14.7.0", "aws-sdk": "^2.1491.0", "redis": "^4.6.11", "prometheus-api-metrics": "^3.2.2", "prom-client": "^15.0.0", "pg": "^8.11.3", "axios": "^1.6.2"}, "devDependencies": {"@types/node": "^20.9.0", "@types/dockerode": "^3.3.23", "@types/jsonwebtoken": "^9.0.5", "@types/node-cron": "^3.0.11", "@types/pg": "^8.10.9", "typescript": "^5.2.2", "tsx": "^4.4.0", "eslint": "^8.54.0", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "jest": "^29.7.0", "@types/jest": "^29.5.8"}, "engines": {"node": ">=18.0.0"}}