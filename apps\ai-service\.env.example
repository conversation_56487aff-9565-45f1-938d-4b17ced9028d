# RP9 AI Service Configuration

# Server Configuration
AI_PORT=8090
AI_HOST=0.0.0.0
NODE_ENV=development

# CORS Configuration
AI_CORS_ORIGINS=http://localhost:3000,https://portal.rp9.io

# Rate Limiting
AI_RATE_LIMIT_MAX=100
AI_RATE_LIMIT_WINDOW=1 minute

# AI Providers
OPENAI_API_KEY=sk-your-openai-key-here
ANTHROPIC_API_KEY=sk-ant-your-anthropic-key-here
AI_MODEL_PRIMARY=gpt-4-turbo-preview
AI_MODEL_FALLBACK=claude-3-sonnet-20240229
AI_ENABLED_PROVIDERS=openai,anthropic

# BYOK (Bring Your Own Key)
AI_ALLOW_BYOK=true

# Cache Configuration
AI_CACHE_TTL_SEC=600
AI_CACHE_MAX_SIZE=1000

# Budget & Cost Control
AI_BUDGET_DEFAULT_USD=20
AI_BUDGET_ENFORCEMENT=warn
AI_COST_PER_1K_TOKENS_USD=0.02

# Database (Supabase)
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key-here

# n8n Integration
N8N_BASE_URL=https://your-n8n-instance.railway.app
N8N_API_KEY=your-n8n-api-key-here
N8N_SANDBOX_TTL_MINUTES=30

# Security
AI_HMAC_SECRET=your-hmac-secret-for-portal-communication
JWT_SECRET=your-jwt-secret-if-needed

# Logging
AI_LOG_LEVEL=info

# PII Redaction
AI_REDACT_PII=true
AI_PII_REPLACEMENT_TOKEN=REDACTED

# Sandbox Configuration
AI_SANDBOX_ENABLED=true
AI_SANDBOX_MAX_DURATION_MS=30000