# RP9 Orchestrator Environment Variables

# =============================================================================
# SERVER CONFIGURATION
# =============================================================================
PORT=8080
NODE_ENV=production
LOG_LEVEL=info

# =============================================================================
# AUTHENTICATION & SECURITY
# =============================================================================
JWT_SECRET=your-super-secure-jwt-secret-change-in-production-min-32-chars
HMAC_SECRET=your-super-secure-hmac-secret-change-in-production-min-32-chars

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
POSTGRES_URL=postgresql://rp9:changeme123@localhost:5432/rp9_platform
DB_PASSWORD=changeme123

# Supabase (for RLS and additional features)
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key

# =============================================================================
# STRIPE CONFIGURATION
# =============================================================================
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key_here

# =============================================================================
# S3 STORAGE CONFIGURATION (for backups)
# =============================================================================
S3_ENDPOINT=https://s3.amazonaws.com
S3_BUCKET=rp9-backups
S3_ACCESS_KEY=your-s3-access-key
S3_SECRET_KEY=your-s3-secret-key

# Alternative S3-compatible providers:
# MinIO: http://localhost:9000
# DigitalOcean Spaces: https://nyc3.digitaloceanspaces.com
# Backblaze B2: https://s3.us-west-002.backblazeb2.com

# =============================================================================
# TRAEFIK & DOMAIN CONFIGURATION
# =============================================================================
TRAEFIK_DOMAIN=rp9.io
ACME_EMAIL=<EMAIL>

# Basic auth for Traefik dashboard (user:password in htpasswd format)
# Generate with: echo $(htpasswd -nb admin password) | sed -e s/\\$/\\$\\$/g
TRAEFIK_BASIC_AUTH=admin:$$2y$$10$$example_bcrypt_hash

# =============================================================================
# N8N CONFIGURATION
# =============================================================================
# Docker image for n8n instances
N8N_IMAGE=n8nio/n8n:latest

# Shared n8n instance (for hybrid tenancy)
SHARED_N8N_BASE_URL=https://primary-production-7f25.up.railway.app
SHARED_N8N_API_KEY=your-shared-n8n-api-key

# =============================================================================
# REDIS CONFIGURATION
# =============================================================================
REDIS_URL=redis://localhost:6379/0
REDIS_PASSWORD=your-redis-password

# =============================================================================
# DOCKER CONFIGURATION
# =============================================================================
DOCKER_SOCKET_PATH=/var/run/docker.sock

# =============================================================================
# MONITORING & OBSERVABILITY
# =============================================================================
# Grafana admin password
GRAFANA_ADMIN_PASSWORD=your-grafana-admin-password

# Slack webhook for alerts
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK

# =============================================================================
# DEVELOPMENT OVERRIDES
# =============================================================================
# Uncomment for local development
# NODE_ENV=development
# LOG_LEVEL=debug
# TRAEFIK_DOMAIN=rp9.local
# POSTGRES_URL=postgresql://postgres:postgres@localhost:5432/rp9_dev

# =============================================================================
# FEATURE FLAGS
# =============================================================================
FF_BLUE_GREEN_DEPLOYMENTS=true
FF_AUTO_SCALING=true
FF_BACKUP_SYSTEM=true
FF_METRICS_COLLECTION=true

# =============================================================================
# SCALING THRESHOLDS
# =============================================================================
# Auto-scaling trigger thresholds
AUTOSCALE_QUEUE_WAIT_P95_THRESHOLD=5.0
AUTOSCALE_CPU_THRESHOLD=80.0
AUTOSCALE_MEMORY_THRESHOLD=85.0
AUTOSCALE_EXECUTIONS_MIN_THRESHOLD=10

# =============================================================================
# BACKUP CONFIGURATION
# =============================================================================
BACKUP_RETENTION_DAILY=7
BACKUP_RETENTION_WEEKLY=4
BACKUP_RETENTION_MONTHLY=12

# Backup schedule (cron format)
BACKUP_SCHEDULE_DAILY=0 2 * * *
BACKUP_SCHEDULE_WEEKLY=0 3 * * 0

# =============================================================================
# RATE LIMITING
# =============================================================================
RATE_LIMIT_REQUESTS_PER_MINUTE=100
RATE_LIMIT_BURST=50

# =============================================================================
# CORS CONFIGURATION
# =============================================================================
ALLOWED_ORIGINS=https://app.rp9.com,https://rp9.netlify.app

# =============================================================================
# INSTRUMENTATION
# =============================================================================
# Optional: OpenTelemetry configuration
OTEL_EXPORTER_OTLP_ENDPOINT=https://api.honeycomb.io
OTEL_EXPORTER_OTLP_HEADERS=x-honeycomb-team=your-api-key
OTEL_SERVICE_NAME=rp9-orchestrator
OTEL_SERVICE_VERSION=1.0.0

# =============================================================================
# USAGE NOTES
# =============================================================================
# 1. Copy this file to .env and fill in your actual values
# 2. Never commit .env files to version control
# 3. Use strong, unique secrets for JWT_SECRET and HMAC_SECRET
# 4. Rotate secrets regularly in production
# 5. Use environment-specific values for different deployments
# 6. Test configuration with docker-compose -f docker-compose.orchestrator.yml config