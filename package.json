{"name": "agente-virtual-ia", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "build:netlify": "next build", "start": "next start", "lint": "next lint --fix", "lint:check": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "export": "next export", "populate-templates": "node scripts/populate-templates.js", "migrate-templates": "node scripts/run-migration.js", "docs": "node docs/server.js", "docs:dev": "node docs/server.js", "export-i18n": "tsx scripts/export-i18n.ts", "export-i18n:locale": "tsx scripts/export-i18n.ts"}, "dependencies": {"@netlify/functions": "^4.2.1", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.12", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@stripe/stripe-js": "^7.8.0", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.54.0", "@types/react-syntax-highlighter": "^15.5.13", "axios": "^1.11.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "crypto-js": "^4.2.0", "date-fns": "^2.30.0", "dotenv": "^17.2.1", "handlebars": "^4.7.8", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.539.0", "marked": "^16.1.2", "next": "15.4.6", "next-intl": "^4.3.4", "next-themes": "^0.4.6", "react": "19.1.0", "react-dom": "19.1.0", "react-syntax-highlighter": "^15.6.1", "reactflow": "^11.11.4", "recharts": "^3.1.2", "sonner": "^2.0.7", "stripe": "^18.4.0", "tailwind-merge": "^3.3.1", "zod": "^3.22.4"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^16.0.1", "@testing-library/user-event": "^14.5.1", "@types/crypto-js": "^4.2.2", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.6", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "next-router-mock": "^0.9.10", "tailwindcss": "^4", "tw-animate-css": "^1.3.6", "typescript": "^5"}}